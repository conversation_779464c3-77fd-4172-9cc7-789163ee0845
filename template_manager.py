import pyJian<PERSON>ing<PERSON><PERSON> as draft
from pathlib import Path
from paragraph_data import Paragraph<PERSON><PERSON>, load_paragraph_data_from_json
from template_loader import TemplateLoader
from template_processor import TemplateProcessor
from template import VideoTemplate
import config


class TemplateManager:
    """Main class for managing and applying video templates"""
    
    def __init__(self, base_draft_path: str | None = None, canvas_width: int = 1080, canvas_height: int = 1920):
        """
        Initialize TemplateManager
        
        Args:
            base_draft_path: Base path for JianYing drafts (default from config.BASE_DRAFT_FOLDER)
            canvas_width: Canvas width in pixels (default from config.DEFAULT_CANVAS_WIDTH)
            canvas_height: Canvas height in pixels (default from config.DEFAULT_CANVAS_HEIGHT)
        """
        # Use config defaults if not provided
        self.base_draft_path: str = base_draft_path or config.BASE_DRAFT_FOLDER
        self.canvas_width: int = canvas_width or config.DEFAULT_CANVAS_WIDTH
        self.canvas_height: int = canvas_height or config.DEFAULT_CANVAS_HEIGHT
        
        self.loader: TemplateLoader = TemplateLoader()
        self.processor: TemplateProcessor = TemplateProcessor(self.canvas_width, self.canvas_height)
        
        # Initialize DraftFolder with the configured path
        # Note: DraftFolder expects the directory to exist
        self.draft_folder: draft.DraftFolder = draft.DraftFolder(self.base_draft_path)
    
    def load_template_from_file(self, template_path: str) -> VideoTemplate:
        """
        Load template from TOML file
        
        Args:
            template_path: Path to TOML template file
        
        Returns:
            Loaded VideoTemplate object
        """
        template = self.loader.load_template(template_path)
        is_valid = self.loader.validate_template(template)
        if is_valid:
            print("Template loaded successfully.")
        else:
            print("Failed to load template.")
        return template
    
    def load_paragraph_data(self, json_path: str) -> list[ParagraphData]:
        """
        Load paragraph timing data from JSON file
        
        Args:
            json_path: Path to paragraph JSON file
        
        Returns:
            List of paragraph data
        """
        return load_paragraph_data_from_json(json_path)
    
    def prepare_materials(self, template: VideoTemplate, 
                         video_paths: list[str] | None = None,
                         image_paths: list[str] | None = None) -> dict[str, list[str]]:
        """
        Prepare materials based on template requirements
        
        Args:
            template: The video template
            video_paths: List of video file paths
            image_paths: List of image file paths
        
        Returns:
            Dictionary of prepared materials
        """
        materials: dict[str, list[str]] = {}
        
        # Check template meta for material requirements
        meta = template.meta
        
        # Prepare videos
        num_videos = meta.get('videos', 0)
        if num_videos > 0:
            if video_paths:
                materials['videos'] = video_paths[:num_videos]
            else:
                print(f"Warning: Template requires {num_videos} videos but none provided")
                materials['videos'] = []
        
        # Prepare images
        num_images = meta.get('images', 0)
        if num_images > 0:
            if image_paths:
                materials['images'] = image_paths[:num_images]
            else:
                print(f"Warning: Template requires {num_images} images but none provided")
                materials['images'] = []
        
        return materials
    
    def apply_template(self, 
                      draft_name: str,
                      template_path: str,
                      paragraph_json_path: str,
                      video_paths: list[str] | None = None,
                      image_paths: list[str] | None = None,
                      override_canvas_width: int | None = None,
                      override_canvas_height: int | None = None) -> str:
        """
        Apply template to create a new draft
        
        Args:
            draft_name: Name for the new draft
            template_path: Path to TOML template file
            paragraph_json_path: Path to paragraph timing JSON
            video_paths: List of video file paths
            image_paths: List of image file paths
            override_canvas_width: Canvas width in pixels (if None, uses template meta or default 1080)
            override_canvas_height: Canvas height in pixels (if None, uses template meta or default 1920)
        
        Returns:
            Path to created draft
        """
        try:
            # Load template
            print(f"Loading template from {template_path}...")
            template = self.load_template_from_file(template_path)
            
            # Get canvas dimensions from template meta or use provided/default values
            if override_canvas_width is None:
                # Safely get canvas_width from meta with type checking
                meta_width = template.meta.get('canvas_width', 1080)
                try:
                    canvas_width: int = int(meta_width)
                except (ValueError, TypeError):
                    canvas_width = 1080
            else:
                canvas_width = override_canvas_width
            if override_canvas_height is None:
                # Safely get canvas_height from meta with type checking
                meta_height = template.meta.get('canvas_height', 1920)
                try:
                    canvas_height: int = int(meta_height)
                except (ValueError, TypeError):
                    canvas_height = 1920
            else:
                canvas_height = override_canvas_height
            
            # Update processor with correct canvas dimensions
            self.processor.canvas_width = canvas_width
            self.processor.canvas_height = canvas_height
            
            print(f"Canvas size: {canvas_width}x{canvas_height}")
            
            # Load paragraph data
            print(f"Loading paragraph data from {paragraph_json_path}...")
            paragraphs_data = self.load_paragraph_data(paragraph_json_path)
            
            # Prepare materials
            print("Preparing materials...")
            materials = self.prepare_materials(template, video_paths, image_paths)
            
            # Create new draft (use config setting for replace)
            print(f"Creating draft '{draft_name}'...")
            script = self.draft_folder.create_draft(
                draft_name, 
                canvas_width, 
                canvas_height, 
                allow_replace=config.ALLOW_REPLACE_DRAFT
            )
            
            # Apply template
            print("Applying template to draft...")
            self.processor.process_template(script, template, paragraphs_data, materials)
            
            # Save draft
            print("Saving draft...")
            script.save()
            
            draft_path = str(Path(self.base_draft_path) / draft_name)
            print(f"Draft created successfully at: {draft_path}")
            
            return draft_path
            
        except Exception as e:
            print(f"Error applying template: {e}")
            raise
    
    def apply_template_to_existing(self,
                                  script: draft.ScriptFile,
                                  template: VideoTemplate,
                                  paragraphs_data: list[ParagraphData],
                                  materials: dict[str, list[str]]) -> None:
        """
        Apply template to an existing script
        
        Args:
            script: Existing pyJianYingDraft script object
            template: The video template
            paragraphs_data: List of paragraph timing data
            materials: Available materials
        """
        self.processor.process_template(script, template, paragraphs_data, materials)


# Example usage function
def example_usage():
    """Example of how to use the TemplateManager"""
    # Initialize manager (will use config.BASE_DRAFT_FOLDER by default)
    manager = TemplateManager()
    
    # Apply template to create new draft
    draft_path = manager.apply_template(
        draft_name="My Video Project",
        template_path="config.toml",
        paragraph_json_path="paragraph.json",
        video_paths=["video1.mp4", "video2.mp4"],
        image_paths=["image1.png"],
        override_canvas_width=1080,
        override_canvas_height=1920
    )
    
    print(f"Created draft at: {draft_path}")


if __name__ == "__main__":
    # Run example if executed directly
    example_usage()