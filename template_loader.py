import toml
from typing import TypedDict, NotRequired, Literal, TypeGuard
from template import (
    RelativePosition,
    VideoTemplate, 
    VideoTemplateDict,
    ParagraphDict,
    TrackDict,
    KeyframeDict,
    MetaDict,
    TrackRange,
    Vector2D,
    MaskConfig,
    EffectParams
)


# TypedDict for raw TOML data structures
class TomlTrackData(TypedDict, total=False):
    """Raw track data from TOML"""
    type: NotRequired[str]
    timing: NotRequired[str]
    keyframe: NotRequired[KeyframeDict | list[KeyframeDict]]
    index: NotRequired[int]
    name: NotRequired[str]
    params: NotRequired[EffectParams | float]  # Can be a single value or dict
    mask: NotRequired[MaskConfig]
    round_corner: NotRequired[int]
    position: NotRequired[Vector2D]
    feather: NotRequired[int]
    center: NotRequired[Vector2D]
    image: NotRequired[str]
    alpha: NotRequired[float]
    effect: NotRequired[str]
    range: NotRequired[TrackRange]


class TomlParagraphData(TypedDict, total=False):
    """Raw paragraph data from TOML"""
    track: NotRequired[TomlTrackData | list[TomlTrackData]]


# TypedDict for raw TOML root structure
class TomlRootData(TypedDict, total=False):
    """Raw root data structure from TOML file"""
    meta: NotRequired[MetaDict]
    paragraph: NotRequired[TomlParagraphData | list[TomlParagraphData]]


# Type guard functions
def is_str_object_dict(value: object) -> TypeGuard[dict[str, object]]:
    """Type guard to check if value is dict[str, object]"""
    if not isinstance(value, dict):
        return False
    # Check all keys are strings using try/except to avoid type warnings
    try:
        return all(isinstance(key, str) for key in value.keys())
    except Exception:
        return False


def is_str_object_dict_list(value: object) -> TypeGuard[list[dict[str, object]]]:
    """Type guard to check if value is list[dict[str, object]]"""
    if not isinstance(value, list):
        return False
    # Check all items are dicts with string keys using try/except
    try:
        for item in value:
            if not isinstance(item, dict):
                return False
            if not all(isinstance(key, str) for key in item.keys()):
                return False
        return True
    except Exception:
        return False


class TemplateLoader:
    """Load and parse TOML templates"""
    
    def _validate_toml_root(self, data: dict[str, object]) -> TomlRootData:
        """Validate and convert raw dict to TomlRootData"""
        result: TomlRootData = {}

        # Handle meta section
        if 'meta' in data and is_str_object_dict(data['meta']):
            result['meta'] = self._validate_meta(data['meta'])

        # Handle paragraph section
        if 'paragraph' in data:
            paragraph_raw = data['paragraph']
            if is_str_object_dict_list(paragraph_raw):
                result['paragraph'] = [self._validate_paragraph(p) for p in paragraph_raw]
            elif is_str_object_dict(paragraph_raw):
                result['paragraph'] = self._validate_paragraph(paragraph_raw)

        return result
    
    def _validate_meta(self, meta_raw: dict[str, object]) -> MetaDict:
        """Validate and convert raw dict to MetaDict"""
        meta: MetaDict = {}
        
        for key, value in meta_raw.items():
            if key == 'title' and isinstance(value, str):
                meta['title'] = value
            elif key == 'description' and isinstance(value, str):
                meta['description'] = value
            elif key == 'version' and isinstance(value, str):
                meta['version'] = value
            elif key == 'name' and isinstance(value, str):
                meta['name'] = value
            elif key == 'videos' and isinstance(value, int):
                meta['videos'] = value
            elif key == 'images' and isinstance(value, int):
                meta['images'] = value
            elif key == 'canvas_width' and isinstance(value, int):
                meta['canvas_width'] = value
            elif key == 'canvas_height' and isinstance(value, int):
                meta['canvas_height'] = value
        
        return meta
    
    def _validate_paragraph(self, para_raw: dict[str, object]) -> TomlParagraphData:
        """Validate and convert raw dict to TomlParagraphData"""
        para: TomlParagraphData = {}

        if 'track' in para_raw:
            track_raw = para_raw['track']
            if is_str_object_dict_list(track_raw):
                para['track'] = [self._validate_track(t) for t in track_raw]
            elif is_str_object_dict(track_raw):
                para['track'] = self._validate_track(track_raw)

        return para
    
    def _validate_track(self, track_raw: dict[str, object]) -> TomlTrackData:
        """Validate and convert raw dict to TomlTrackData"""
        track: TomlTrackData = {}
        
        for key, value in track_raw.items():
            if key == 'type' and isinstance(value, str):
                track['type'] = value
            elif key == 'timing' and isinstance(value, str):
                track['timing'] = value
            elif key == 'index' and isinstance(value, int):
                track['index'] = value
            elif key == 'name' and isinstance(value, str):
                track['name'] = value
            elif key == 'params':
                if isinstance(value, (int, float)):
                    # Single param value - convert to dict
                    track['params'] = {'value': float(value)}
                elif is_str_object_dict(value):
                    track['params'] = self._validate_effect_params(value)
            elif key == 'mask' and is_str_object_dict(value):
                track['mask'] = self._validate_mask_config(value)
            elif key == 'round_corner' and isinstance(value, int):
                track['round_corner'] = value
            elif key == 'position' and is_str_object_dict(value):
                pos = self._validate_vector2d(value)
                if pos:
                    track['position'] = pos
            elif key == 'feather' and isinstance(value, int):
                track['feather'] = value
            elif key == 'center' and is_str_object_dict(value):
                center = self._validate_vector2d(value)
                if center:
                    track['center'] = center
            elif key == 'image' and isinstance(value, str):
                track['image'] = value
            elif key == 'alpha' and isinstance(value, (int, float)):
                track['alpha'] = float(value)
            elif key == 'effect' and isinstance(value, str):
                track['effect'] = value
            elif key == 'range' and is_str_object_dict(value):
                range_val = self._validate_track_range(value)
                if range_val:
                    track['range'] = range_val
            elif key == 'keyframe':
                if is_str_object_dict_list(value):
                    keyframes = [self._validate_keyframe(kf) for kf in value]
                    track['keyframe'] = [kf for kf in keyframes if kf]  # Filter out None
                elif is_str_object_dict(value):
                    kf = self._validate_keyframe(value)
                    if kf:
                        track['keyframe'] = kf
        
        return track
    
    def _validate_vector2d(self, value: dict[str, object]) -> Vector2D | None:
        """Validate and convert raw dict to Vector2D"""
        if 'x' in value and 'y' in value:
            x_val = value['x']
            y_val = value['y']
            if isinstance(x_val, (int, float)) and isinstance(y_val, (int, float)):
                return {'x': float(x_val), 'y': float(y_val)}
        return None
    
    def _validate_effect_params(self, value: dict[str, object]) -> EffectParams:
        """Validate and convert raw dict to EffectParams"""
        params: EffectParams = {}
        
        for key, val in value.items():
            if key == 'intensity' and isinstance(val, (int, float)):
                params['intensity'] = float(val)
            elif key == 'duration' and isinstance(val, (int, float)):
                params['duration'] = float(val)
            elif key == 'color' and isinstance(val, str):
                params['color'] = val
            elif key == 'blend_mode' and isinstance(val, str):
                params['blend_mode'] = val
            elif key == 'opacity' and isinstance(val, (int, float)):
                params['opacity'] = float(val)
            elif key == 'scale' and isinstance(val, (int, float)):
                params['scale'] = float(val)
            elif key == 'value' and isinstance(val, (int, float)):
                params['value'] = float(val)
        
        return params
    
    def _validate_mask_config(self, value: dict[str, object]) -> MaskConfig:
        """Validate and convert raw dict to MaskConfig"""
        mask: MaskConfig = {}
        
        for key, val in value.items():
            if key == 'type' and isinstance(val, str):
                mask['type'] = val
            elif key == 'radius' and isinstance(val, (int, float)):
                mask['radius'] = float(val)
            elif key == 'width' and isinstance(val, (int, float)):
                mask['width'] = float(val)
            elif key == 'height' and isinstance(val, (int, float)):
                mask['height'] = float(val)
            elif key == 'round_corner' and isinstance(val, (int, float)):
                mask['round_corner'] = float(val)
            elif key == 'position' and is_str_object_dict(val):
                pos = self._validate_vector2d(val)
                if pos:
                    mask['position'] = pos
            elif key == 'feather' and isinstance(val, (int, float)):
                mask['feather'] = float(val)
            elif key == 'center' and is_str_object_dict(val):
                center = self._validate_vector2d(val)
                if center:
                    mask['center'] = center
        
        return mask
    
    def _validate_track_range(self, value: dict[str, object]) -> TrackRange | None:
        """Validate and convert raw dict to TrackRange"""
        start_pos: RelativePosition | None = None
        end_pos: RelativePosition | None = None
        
        if 'start' in value and is_str_object_dict(value['start']):
            start_pos = self._validate_relative_position(value['start'])

        if 'end' in value and is_str_object_dict(value['end']):
            end_pos = self._validate_relative_position(value['end'])
        
        # TrackRange requires both start and end
        if start_pos and end_pos:
            range_dict: TrackRange = {
                'start': start_pos,
                'end': end_pos
            }
            return range_dict
        
        # Return None if incomplete
        return None
    
    def _validate_relative_position(self, value: dict[str, object]) -> RelativePosition | None:
        """Validate and convert raw dict to RelativePosition"""
        if 'type' not in value or 'position' not in value:
            return None
        
        type_val = value['type']
        pos_val = value['position']
        
        if not isinstance(type_val, str) or not isinstance(pos_val, str):
            return None
        
        # Validate and narrow type literal
        rel_type: Literal['pre', 'middle', 'tail']
        if type_val == 'pre':
            rel_type = 'pre'
        elif type_val == 'middle':
            rel_type = 'middle'
        elif type_val == 'tail':
            rel_type = 'tail'
        else:
            return None
        
        # Validate and narrow position literal
        rel_position: Literal['start', 'end']
        if pos_val == 'start':
            rel_position = 'start'
        elif pos_val == 'end':
            rel_position = 'end'
        else:
            return None

        rel_pos: RelativePosition = {
            'type': rel_type,
            'position': rel_position
        }
        
        if 'offset_frame' in value and isinstance(value['offset_frame'], int):
            rel_pos['offset_frame'] = value['offset_frame']
        
        if 'index' in value and isinstance(value['index'], int):
            rel_pos['index'] = value['index']
        
        return rel_pos
    
    def _validate_keyframe(self, value: dict[str, object]) -> KeyframeDict | None:
        """Validate and convert raw dict to KeyframeDict"""
        if 'relative' not in value:
            return None
        
        rel_raw = value['relative']
        if not is_str_object_dict(rel_raw):
            return None

        relative = self._validate_relative_position(rel_raw)
        if not relative:
            return None

        keyframe: KeyframeDict = {'relative': relative}

        # Add optional fields
        if 'scale' in value and is_str_object_dict(value['scale']):
            scale = self._validate_vector2d(value['scale'])
            if scale:
                keyframe['scale'] = scale

        if 'position' in value and is_str_object_dict(value['position']):
            pos = self._validate_vector2d(value['position'])
            if pos:
                keyframe['position'] = pos
        
        if 'rotation' in value and isinstance(value['rotation'], (int, float)):
            keyframe['rotation'] = float(value['rotation'])
        
        if 'alpha' in value and isinstance(value['alpha'], (int, float)):
            keyframe['alpha'] = float(value['alpha'])
        
        return keyframe
    
    def load_template(self, toml_path: str) -> VideoTemplate:
        """Parse TOML file into VideoTemplate object"""
        # Load TOML file
        with open(toml_path, 'r', encoding='utf-8') as f:
            raw_data: dict[str, object] = toml.load(f)
        
        # Validate and convert to TomlRootData
        toml_data = self._validate_toml_root(raw_data)
        
        # Parse and restructure the data for VideoTemplate
        template_data = self._restructure_toml_data(toml_data)
        
        # Create VideoTemplate from parsed data
        return VideoTemplate.from_dict(template_data)
    
    def _restructure_toml_data(self, data: TomlRootData) -> VideoTemplateDict:
        """Restructure TOML data to match VideoTemplate structure"""
        # Use already validated meta
        meta = data.get('meta', {})
        
        result: VideoTemplateDict = {
            'meta': meta,
            'paragraph': []
        }
        
        # Process paragraph sections (already validated)
        paragraph_raw = data.get('paragraph')
        if paragraph_raw is not None:
            # Ensure paragraphs is a list
            paragraphs_list: list[TomlParagraphData]
            if isinstance(paragraph_raw, list):
                paragraphs_list = paragraph_raw
            else:
                paragraphs_list = [paragraph_raw]
            
            for paragraph in paragraphs_list:
                paragraph_dict: ParagraphDict = {'track': []}
                
                # Process tracks in the paragraph
                track_raw = paragraph.get('track')
                if track_raw is None:
                    continue
                    
                # Ensure tracks is a list
                tracks_list: list[TomlTrackData]
                if isinstance(track_raw, list):
                    tracks_list = track_raw
                else:
                    tracks_list = [track_raw]
                    
                for track in tracks_list:
                    # Get type and timing with proper validation
                    track_type_raw = track.get('type', 'video')
                    track_timing_raw = track.get('timing', 'relative')
                    
                    # Validate type field
                    track_type: Literal['video', 'audio', 'effect', 'image', 'text']
                    if track_type_raw == 'video':
                        track_type = 'video'
                    elif track_type_raw == 'audio':
                        track_type = 'audio'
                    elif track_type_raw == 'effect':
                        track_type = 'effect'
                    elif track_type_raw == 'image':
                        track_type = 'image'
                    elif track_type_raw == 'text':
                        track_type = 'text'
                    else:
                        track_type = 'video'  # default
                    
                    # Validate timing field  
                    track_timing: Literal['relative', 'absolute']
                    if track_timing_raw == 'absolute':
                        track_timing = 'absolute'
                    else:
                        track_timing = 'relative'  # default
                    
                    track_dict: TrackDict = {
                        'type': track_type,
                        'timing': track_timing,
                        'keyframe': []
                    }
                    
                    # Copy other track properties (already validated)
                    if 'index' in track:
                        track_dict['index'] = track['index']
                    if 'name' in track:
                        track_dict['name'] = track['name']
                    if 'params' in track:
                        # params can be a float or EffectParams dict
                        params_val = track['params']
                        if isinstance(params_val, dict):
                            track_dict['params'] = params_val
                        elif isinstance(params_val, float):
                            # Convert single value to dict format
                            track_dict['params'] = {'value': params_val}
                    if 'mask' in track:
                        track_dict['mask'] = track['mask']
                    if 'round_corner' in track:
                        track_dict['round_corner'] = track['round_corner']
                    if 'position' in track:
                        track_dict['position'] = track['position']
                    if 'feather' in track:
                        track_dict['feather'] = track['feather']
                    if 'center' in track:
                        track_dict['center'] = track['center']
                    if 'image' in track:
                        track_dict['image'] = track['image']
                    if 'alpha' in track:
                        track_dict['alpha'] = track['alpha']
                    if 'effect' in track:
                        track_dict['effect'] = track['effect']
                    if 'range' in track and track['range']:
                        track_dict['range'] = track['range']
                    
                    # Process keyframes (already validated)
                    if 'keyframe' in track:
                        keyframe_raw = track['keyframe']
                        if isinstance(keyframe_raw, list):
                            track_dict['keyframe'] = keyframe_raw
                        else:
                            track_dict['keyframe'] = [keyframe_raw]
                    
                    paragraph_dict['track'].append(track_dict)
                
                result['paragraph'].append(paragraph_dict)
        
        return result
    
    def validate_template(self, template: VideoTemplate) -> bool:
        """Validate template structure and required fields"""
        # Check meta section
        if not template.meta:
            raise ValueError("Template must have a meta section")
        
        if 'name' not in template.meta:
            raise ValueError("Template meta must have a name")
        
        # Check paragraphs
        if not template.paragraphs:
            raise ValueError("Template must have at least one paragraph")
        
        # Validate each paragraph
        for i, paragraph in enumerate(template.paragraphs):
            if not paragraph.tracks:
                raise ValueError(f"Paragraph {i} must have at least one track")
            
            # Validate tracks
            for j, track in enumerate(paragraph.tracks):
                if track.type not in ['video', 'audio', 'effect', 'image', 'text']:
                    raise ValueError(f"Invalid track type: {track.type} in paragraph {i}, track {j}")
                
                if track.timing not in ['relative', 'absolute']:
                    raise ValueError(f"Invalid timing: {track.timing} in paragraph {i}, track {j}")
                
                # Validate keyframes
                for k, keyframe in enumerate(track.keyframes):
                    if not keyframe.relative:
                        raise ValueError(f"Keyframe must have relative positioning in paragraph {i}, track {j}, keyframe {k}")
                    
                    if 'type' not in keyframe.relative:
                        raise ValueError(f"Keyframe relative must have type (pre/middle/tail) in paragraph {i}, track {j}, keyframe {k}")
                    
                    if keyframe.relative['type'] not in ['pre', 'middle', 'tail']:
                        raise ValueError(f"Invalid keyframe relative type: {keyframe.relative['type']} in paragraph {i}, track {j}, keyframe {k}")
                    
                    if 'position' not in keyframe.relative:
                        raise ValueError(f"Keyframe relative must have position (start/end) in paragraph {i}, track {j}, keyframe {k}")
                    
                    if keyframe.relative['position'] not in ['start', 'end']:
                        raise ValueError(f"Invalid keyframe relative position: {keyframe.relative['position']} in paragraph {i}, track {j}, keyframe {k}")
                    
                    # Check for index in middle type
                    if keyframe.relative['type'] == 'middle' and keyframe.relative['position'] == 'start':
                        if 'index' not in keyframe.relative:
                            # If no index specified, default to 0
                            keyframe.relative['index'] = 0
        
        return True