import pyJian<PERSON>ingDraft as draft

# Test the correct usage of add_keyframe method
print("Testing add_keyframe method...")

# Create a simple script
folder = draft.DraftFolder("C:\\All\\Tools\\JianyingPro Drafts")
script = folder.create_draft(
    "test_keyframe_usage",
    width=1080,
    height=1920,
    allow_replace=True
)

# Add a video track
script.add_track(draft.TrackType.video, "video_0")

# Create video material
mat = draft.VideoMaterial("图片合集/black.png")

# Create video segment
seg = draft.VideoSegment(
    mat,
    source_timerange=draft.trange(0, 5000000),
    target_timerange=draft.trange(0, 5000000)
)

# Test add_keyframe with different parameter counts
print("\nTesting add_keyframe method signature...")

try:
    # Try with 3 parameters (property, timestamp, value)
    result = seg.add_keyframe(
        draft.KeyframeProperty.uniform_scale,
        1000000,  # 1 second
        1.5       # scale value
    )
    print(f"3 parameters - Success: {result}")
except Exception as e:
    print(f"3 parameters - Error: {e}")

try:
    # Try with 2 parameters (property, value)
    result = seg.add_keyframe(
        draft.KeyframeProperty.uniform_scale,
        1.5       # scale value
    )
    print(f"2 parameters - Success: {result}")
except Exception as e:
    print(f"2 parameters - Error: {e}")

# Check what methods are available on the segment
print(f"\nSegment methods containing 'keyframe':")
for attr in dir(seg):
    if 'keyframe' in attr.lower():
        print(f"  {attr}")

# Check KeyframeProperty enum values
print(f"\nKeyframeProperty values:")
for attr in dir(draft.KeyframeProperty):
    if not attr.startswith('_'):
        print(f"  {attr}: {getattr(draft.KeyframeProperty, attr)}")
